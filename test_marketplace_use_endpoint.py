#!/usr/bin/env python3
"""
Test script to verify the updated marketplace /use endpoint functionality.
This script tests that the endpoint now returns agent/workflow data when using items.
"""

import json
import requests
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"  # Adjust as needed
API_BASE = f"{BASE_URL}/marketplace"

def test_use_agent_endpoint():
    """Test the /use endpoint with an agent item."""
    print("Testing /use endpoint with AGENT item type...")
    
    # Sample request data
    request_data = {
        "item_id": "sample-agent-id",  # Replace with actual agent ID
        "item_type": "AGENT"
    }
    
    # Headers (you'll need to add proper authentication)
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer YOUR_TOKEN_HERE"  # Replace with actual token
    }
    
    try:
        response = requests.post(
            f"{API_BASE}/use",
            json=request_data,
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Response structure:")
            print(f"- success: {data.get('success')}")
            print(f"- message: {data.get('message')}")
            print(f"- item_id: {data.get('item_id')}")
            print(f"- item_type: {data.get('item_type')}")
            print(f"- use_count: {data.get('use_count')}")
            
            # Check if agent data is returned
            agent_data = data.get('agent')
            if agent_data:
                print("✅ Agent data returned successfully!")
                print(f"- Agent ID: {agent_data.get('id')}")
                print(f"- Agent Name: {agent_data.get('name')}")
                print(f"- MCPs count: {len(agent_data.get('mcps', []))}")
                print(f"- Workflows count: {len(agent_data.get('workflows', []))}")
            else:
                print("❌ No agent data returned")
                
            # Check if workflow data is None (should be for agent)
            workflow_data = data.get('workflow')
            if workflow_data is None:
                print("✅ Workflow data correctly None for agent item")
            else:
                print("❌ Unexpected workflow data for agent item")
                
        else:
            print(f"❌ Request failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing agent endpoint: {str(e)}")

def test_use_workflow_endpoint():
    """Test the /use endpoint with a workflow item."""
    print("\nTesting /use endpoint with WORKFLOW item type...")
    
    # Sample request data
    request_data = {
        "item_id": "sample-workflow-id",  # Replace with actual workflow ID
        "item_type": "WORKFLOW"
    }
    
    # Headers (you'll need to add proper authentication)
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer YOUR_TOKEN_HERE"  # Replace with actual token
    }
    
    try:
        response = requests.post(
            f"{API_BASE}/use",
            json=request_data,
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Response structure:")
            print(f"- success: {data.get('success')}")
            print(f"- message: {data.get('message')}")
            print(f"- item_id: {data.get('item_id')}")
            print(f"- item_type: {data.get('item_type')}")
            print(f"- use_count: {data.get('use_count')}")
            
            # Check if workflow data is returned
            workflow_data = data.get('workflow')
            if workflow_data:
                print("✅ Workflow data returned successfully!")
                print(f"- Workflow ID: {workflow_data.get('id')}")
                print(f"- Workflow Name: {workflow_data.get('name')}")
                print(f"- Description: {workflow_data.get('description')}")
            else:
                print("❌ No workflow data returned")
                
            # Check if agent data is None (should be for workflow)
            agent_data = data.get('agent')
            if agent_data is None:
                print("✅ Agent data correctly None for workflow item")
            else:
                print("❌ Unexpected agent data for workflow item")
                
        else:
            print(f"❌ Request failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing workflow endpoint: {str(e)}")

def test_use_mcp_endpoint():
    """Test the /use endpoint with an MCP item."""
    print("\nTesting /use endpoint with MCP item type...")
    
    # Sample request data
    request_data = {
        "item_id": "sample-mcp-id",  # Replace with actual MCP ID
        "item_type": "MCP"
    }
    
    # Headers (you'll need to add proper authentication)
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer YOUR_TOKEN_HERE"  # Replace with actual token
    }
    
    try:
        response = requests.post(
            f"{API_BASE}/use",
            json=request_data,
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("Response structure:")
            print(f"- success: {data.get('success')}")
            print(f"- message: {data.get('message')}")
            print(f"- item_id: {data.get('item_id')}")
            print(f"- item_type: {data.get('item_type')}")
            print(f"- use_count: {data.get('use_count')}")
            
            # Check that both agent and workflow data are None for MCP
            agent_data = data.get('agent')
            workflow_data = data.get('workflow')
            
            if agent_data is None and workflow_data is None:
                print("✅ Agent and workflow data correctly None for MCP item")
            else:
                print("❌ Unexpected agent or workflow data for MCP item")
                
        else:
            print(f"❌ Request failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error testing MCP endpoint: {str(e)}")

def print_schema_info():
    """Print information about the expected response schema."""
    print("\n" + "="*60)
    print("EXPECTED RESPONSE SCHEMA")
    print("="*60)
    print("""
UseMarketplaceItemResponse:
{
    "success": bool,
    "message": str,
    "item_id": str,
    "item_type": "AGENT" | "WORKFLOW" | "MCP",
    "use_count": int,
    "agent": AgentWithMCPsInDB | null,  // Only for AGENT items
    "workflow": WorkflowInDB | null     // Only for WORKFLOW items
}

AgentWithMCPsInDB includes:
- Basic agent fields (id, name, description, etc.)
- mcps: List of associated MCP servers
- workflows: List of associated workflows

WorkflowInDB includes:
- Basic workflow fields (id, name, description, etc.)
- workflow_definition, workflow_steps, etc.
    """)

if __name__ == "__main__":
    print("🧪 Testing Updated Marketplace /use Endpoint")
    print("="*50)
    
    print_schema_info()
    
    print("\n" + "="*50)
    print("RUNNING TESTS")
    print("="*50)
    
    # Note: You'll need to replace the sample IDs and add proper authentication
    print("⚠️  Note: Update the sample IDs and authentication token before running!")
    
    # Uncomment these lines when you have proper test data:
    # test_use_agent_endpoint()
    # test_use_workflow_endpoint()
    # test_use_mcp_endpoint()
    
    print("\n✅ Test script ready. Update configuration and uncomment test calls to run.")

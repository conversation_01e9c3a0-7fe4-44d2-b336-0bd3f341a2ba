import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from fastapi.testclient import TestClient
from app.main import app
from app.grpc_ import provider_pb2


@pytest.fixture
def client():
    """Create a test client."""
    return TestClient(app)


@pytest.fixture
def mock_provider_service():
    """Mock the provider service client."""
    with patch("app.api.routers.provider_routes.provider_service") as mock:
        yield mock


@pytest.fixture
def mock_auth():
    """Mock authentication."""
    def mock_role_required(roles):
        def decorator(func):
            def wrapper(*args, **kwargs):
                # Inject a mock user into the function
                return func(*args, **kwargs, current_user={"user_id": "test_user", "role": "admin"})
            return wrapper
        return decorator
    
    with patch("app.core.auth_guard.role_required", side_effect=mock_role_required):
        yield


class TestSyncModelsEndpoint:
    """Test cases for the sync models endpoint."""

    def test_sync_models_success(self, client, mock_provider_service, mock_auth):
        """Test successful sync models operation."""
        # Mock the gRPC response
        mock_stats = MagicMock()
        mock_stats.providersAdded = 2
        mock_stats.providersUpdated = 1
        mock_stats.providersRemoved = 0
        mock_stats.modelsAdded = 10
        mock_stats.modelsUpdated = 5
        mock_stats.modelsRemoved = 2
        mock_stats.totalProcessed = 20

        mock_response = MagicMock()
        mock_response.success = True
        mock_response.message = "Models synchronized successfully"
        mock_response.stats = mock_stats

        mock_provider_service.sync_models = AsyncMock(return_value=mock_response)

        # Mock MessageToDict to return the expected format
        with patch("app.api.routers.provider_routes.MessageToDict") as mock_message_to_dict:
            mock_message_to_dict.return_value = {
                "providersAdded": 2,
                "providersUpdated": 1,
                "providersRemoved": 0,
                "modelsAdded": 10,
                "modelsUpdated": 5,
                "modelsRemoved": 2,
                "totalProcessed": 20
            }

            response = client.post("/api/v1/providers/sync-models")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Models synchronized successfully"
        assert data["stats"]["providersAdded"] == 2
        assert data["stats"]["modelsAdded"] == 10
        assert data["stats"]["totalProcessed"] == 20

        # Verify the service was called
        mock_provider_service.sync_models.assert_called_once()

    def test_sync_models_failure(self, client, mock_provider_service, mock_auth):
        """Test sync models operation failure."""
        mock_response = MagicMock()
        mock_response.success = False
        mock_response.message = "Sync failed: External API unavailable"

        mock_provider_service.sync_models = AsyncMock(return_value=mock_response)

        response = client.post("/api/v1/providers/sync-models")

        assert response.status_code == 400
        data = response.json()
        assert "Sync failed: External API unavailable" in data["detail"]

    def test_sync_models_grpc_exception(self, client, mock_provider_service, mock_auth):
        """Test sync models with gRPC exception."""
        mock_provider_service.sync_models = AsyncMock(
            side_effect=Exception("gRPC connection failed")
        )

        response = client.post("/api/v1/providers/sync-models")

        assert response.status_code == 500
        data = response.json()
        assert "Internal server error" in data["detail"] or "gRPC connection failed" in data["detail"]

    def test_sync_models_unauthorized(self, client, mock_provider_service):
        """Test sync models without proper authentication."""
        with patch("app.core.auth_guard.role_required") as mock_auth:
            mock_auth.side_effect = Exception("Unauthorized")

            response = client.post("/api/v1/providers/sync-models")

            # The exact status code depends on how auth_guard handles the exception
            assert response.status_code in [401, 403, 500]

    def test_sync_models_admin_role_required(self, client, mock_provider_service):
        """Test that sync models requires admin role."""
        with patch("app.core.auth_guard.role_required") as mock_auth:
            # Mock non-admin user
            mock_auth.return_value = lambda: {"user_id": "test_user", "role": "user"}
            
            # This should be handled by the auth_guard, but let's test the endpoint behavior
            mock_response = MagicMock()
            mock_response.success = True
            mock_response.message = "Models synchronized successfully"
            mock_response.stats = None

            mock_provider_service.sync_models = AsyncMock(return_value=mock_response)

            response = client.post("/api/v1/providers/sync-models")

            # If auth passes, the endpoint should work
            # In real scenario, auth_guard would prevent non-admin access
            if response.status_code == 200:
                assert response.json()["success"] is True

    def test_sync_models_with_empty_stats(self, client, mock_provider_service, mock_auth):
        """Test sync models with empty stats."""
        mock_response = MagicMock()
        mock_response.success = True
        mock_response.message = "No changes needed"
        mock_response.stats = None

        mock_provider_service.sync_models = AsyncMock(return_value=mock_response)

        response = client.post("/api/v1/providers/sync-models")

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "No changes needed"
        assert data["stats"] is None


class TestProviderServiceClient:
    """Test cases for the provider service client sync_models method."""

    @patch("app.services.provider_service.grpc")
    @patch("app.services.provider_service.provider_pb2")
    def test_sync_models_client_success(self, mock_pb2, mock_grpc):
        """Test the provider service client sync_models method."""
        from app.services.provider_service import ProviderServiceClient

        # Mock the gRPC stub and response
        mock_stub = MagicMock()
        mock_response = MagicMock()
        mock_response.success = True
        mock_response.message = "Sync completed"
        
        mock_stub.SyncModels.return_value = mock_response
        
        # Mock the channel and stub creation
        mock_channel = MagicMock()
        mock_grpc.insecure_channel.return_value = mock_channel
        
        with patch("app.services.provider_service.provider_pb2_grpc.ProviderServiceStub") as mock_stub_class:
            mock_stub_class.return_value = mock_stub
            
            client = ProviderServiceClient()
            
            # Test the sync_models method
            import asyncio
            result = asyncio.run(client.sync_models())
            
            assert result == mock_response
            mock_stub.SyncModels.assert_called_once()
            
            # Verify the request was created correctly
            call_args = mock_stub.SyncModels.call_args[0][0]
            assert isinstance(call_args, type(mock_pb2.SyncModelsRequest.return_value))

    @patch("app.services.provider_service.grpc")
    def test_sync_models_client_grpc_error(self, mock_grpc):
        """Test the provider service client sync_models method with gRPC error."""
        from app.services.provider_service import ProviderServiceClient
        from fastapi import HTTPException

        # Mock gRPC error
        mock_error = mock_grpc.RpcError()
        mock_error.code.return_value = mock_grpc.StatusCode.INTERNAL
        mock_error.details.return_value = "Internal server error"
        
        mock_stub = MagicMock()
        mock_stub.SyncModels.side_effect = mock_error
        
        mock_channel = MagicMock()
        mock_grpc.insecure_channel.return_value = mock_channel
        
        with patch("app.services.provider_service.provider_pb2_grpc.ProviderServiceStub") as mock_stub_class:
            mock_stub_class.return_value = mock_stub
            
            client = ProviderServiceClient()
            
            # Test that gRPC error is properly handled
            import asyncio
            with pytest.raises(HTTPException) as exc_info:
                asyncio.run(client.sync_models())
            
            assert exc_info.value.status_code == 500
import pytest
from fastapi.testclient import TestClient

class TestAdminRoutes:
    @pytest.fixture(autouse=True)
    def setup(self, test_client: TestClient, admin_headers, mock_admin_data):
        self.client = test_client
        self.headers = admin_headers
        self.admin_data = mock_admin_data

    def test_create_role(self):
        role_data = {
            "name": "editor",
            "description": "Editor role",
            "permissions": ["read", "write"]
        }
        response = self.client.post(
            "/api/v1/roles",
            json=role_data,
            headers=self.headers
        )
        assert response.status_code == 200
        assert response.json()["name"] == role_data["name"]

    def test_get_role(self):
        response = self.client.get(
            "/api/v1/roles/1",
            headers=self.headers
        )
        assert response.status_code in [200, 404]

    def test_admin_login(self):
        response = self.client.post(
            "/api/v1/admin/auth/login",
            json={
                "email": self.admin_data["email"],
                "password": self.admin_data["password"]
            }
        )
        assert response.status_code == 200
        assert "access_token" in response.json()

    def test_list_users(self):
        response = self.client.get(
            "/api/v1/admin/users",
            headers=self.headers
        )
        assert response.status_code == 200
        assert isinstance(response.json(), list)
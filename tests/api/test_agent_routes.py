import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi.testclient import TestClient
from app.main import app
from app.schemas.agent import AgentCombinedUpdatePayload, AgentInDB


@pytest.fixture
def client():
    return TestClient(app)


@pytest.fixture
def mock_user_validation():
    """Mock user validation to return success"""
    with patch("app.api.routers.agent_routes.user_service.validate_user") as mock:
        mock.return_value = {
            "success": True,
            "user": {"id": "user123", "name": "Test User"}
        }
        yield mock


@pytest.fixture
def mock_auth():
    """Mock authentication to return a valid user"""
    with patch("app.core.auth_guard.role_required") as mock:
        mock.return_value = lambda: {"user_id": "user123", "role": "user"}
        yield mock


@pytest.fixture
def mock_agent_service():
    """Mock agent service responses"""
    with patch("app.api.routers.agent_routes.agent_service") as mock:
        # Mock successful update response
        mock_response = MagicMock()
        mock_response.success = True
        mock_response.message = "Agent updated successfully"
        mock_response.agent = MagicMock()
        
        # Mock agent dict for MessageToDict conversion
        mock_agent_dict = {
            "id": "agent123",
            "name": "Updated Agent",
            "description": "Updated description",
            "system_message": "Updated system message",
            "model_provider": "openai",
            "model_name": "gpt-4",
            "temperature": 0.8,
            "max_tokens": 2000,
            "files": [],
            "urls": [],
            "organization_id": "org123"
        }
        
        mock.update_agent_combined.return_value = mock_response
        
        with patch("app.api.routers.agent_routes.MessageToDict") as mock_msg_to_dict:
            mock_msg_to_dict.return_value = mock_agent_dict
            yield mock


class TestAgentCombinedUpdate:
    """Test cases for the comprehensive agent combined update endpoint"""
    
    def test_combined_update_core_details_only(self, client, mock_auth, mock_user_validation, mock_agent_service):
        """Test updating only core details fields"""
        payload = {
            "name": "Updated Agent Name",
            "description": "Updated description",
            "model_provider": "openai",
            "model_name": "gpt-4",
            "temperature": 0.8
        }
        
        with patch("app.api.routers.agent_routes.role_required", return_value=lambda: {"user_id": "user123", "role": "user"}):
            response = client.patch("/agents/agent123/combined", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["message"] == "Agent updated successfully"
        assert "agent" in data
        
        # Verify the service was called with correct parameters
        mock_agent_service.update_agent_combined.assert_called_once()
        call_args = mock_agent_service.update_agent_combined.call_args
        assert call_args[1]["agent_id"] == "agent123"
        assert "name" in call_args[1]["update_payload"]
        assert "model_provider" in call_args[1]["update_payload"]
    
    def test_combined_update_multiple_sections(self, client, mock_auth, mock_user_validation, mock_agent_service):
        """Test updating multiple sections in one request"""
        payload = {
            "name": "Comprehensive Agent",
            "system_message": "You are a helpful assistant",
            "model_provider": "openai",
            "temperature": 0.7,
            "files": ["https://drive.google.com/file/d/abc123"],
            "urls": ["https://example.com/docs"],
            "capabilities": [
                {
                    "title": "Data Analysis",
                    "description": "Can analyze data and create reports"
                }
            ],
            "variables": [
                {
                    "name": "api_key",
                    "description": "API key for external service",
                    "type": "text",
                    "default_value": "default_key"
                }
            ],
            "tags": ["analytics", "reporting"],
            "status": "active"
        }
        
        with patch("app.api.routers.agent_routes.role_required", return_value=lambda: {"user_id": "user123", "role": "user"}):
            response = client.patch("/agents/agent123/combined", json=payload)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        
        # Verify all sections were included in the update
        call_args = mock_agent_service.update_agent_combined.call_args
        update_payload = call_args[1]["update_payload"]
        assert "name" in update_payload
        assert "files" in update_payload
        assert "capabilities" in update_payload
        assert "variables" in update_payload
        assert "tags" in update_payload
    
    def test_combined_update_empty_payload(self, client, mock_auth, mock_user_validation, mock_agent_service):
        """Test that empty payload returns error"""
        payload = {}
        
        with patch("app.api.routers.agent_routes.role_required", return_value=lambda: {"user_id": "user123", "role": "user"}):
            response = client.patch("/agents/agent123/combined", json=payload)
        
        assert response.status_code == 400
        data = response.json()
        assert "No fields provided for combined update" in data["detail"]
    
    def test_combined_update_with_google_drive_sync(self, client, mock_auth, mock_user_validation, mock_agent_service):
        """Test that Google Drive sync is triggered when files/urls are updated"""
        payload = {
            "files": ["https://drive.google.com/file/d/abc123"],
            "urls": ["https://example.com/docs"]
        }
        
        # Mock the agent dict to include files and urls
        mock_agent_dict = {
            "id": "agent123",
            "files": [{"file": "https://drive.google.com/file/d/abc123"}],
            "urls": [{"url": "https://example.com/docs"}],
            "organization_id": "org123"
        }
        
        with patch("app.api.routers.agent_routes.MessageToDict", return_value=mock_agent_dict), \
             patch("app.api.routers.agent_routes.asyncio.create_task") as mock_create_task, \
             patch("app.api.routers.agent_routes.role_required", return_value=lambda: {"user_id": "user123", "role": "user"}):
            
            response = client.patch("/agents/agent123/combined", json=payload)
        
        assert response.status_code == 200
        # Verify that background sync task was created
        mock_create_task.assert_called_once()
    
    def test_combined_update_service_error(self, client, mock_auth, mock_user_validation):
        """Test handling of service errors"""
        payload = {"name": "Test Agent"}
        
        with patch("app.api.routers.agent_routes.agent_service") as mock_service:
            mock_response = MagicMock()
            mock_response.success = False
            mock_response.message = "Agent not found"
            mock_service.update_agent_combined.return_value = mock_response
            
            with patch("app.api.routers.agent_routes.role_required", return_value=lambda: {"user_id": "user123", "role": "user"}):
                response = client.patch("/agents/agent123/combined", json=payload)
        
        assert response.status_code == 400
        data = response.json()
        assert data["detail"] == "Agent not found"


class TestAgentCombinedUpdatePayloadSchema:
    """Test the AgentCombinedUpdatePayload schema validation"""
    
    def test_all_fields_optional(self):
        """Test that all fields in the payload are optional"""
        # Empty payload should be valid
        payload = AgentCombinedUpdatePayload()
        assert payload is not None
        
        # Payload with any combination of fields should be valid
        payload = AgentCombinedUpdatePayload(
            name="Test",
            temperature=0.5,
            files=["file1"],
            capabilities=[{"title": "Test", "description": "Test capability"}]
        )
        assert payload.name == "Test"
        assert payload.temperature == 0.5
        assert len(payload.files) == 1
        assert len(payload.capabilities) == 1
    
    def test_field_validation(self):
        """Test field validation rules"""
        # Name should have minimum length
        with pytest.raises(ValueError):
            AgentCombinedUpdatePayload(name="")
        
        # Valid name should work
        payload = AgentCombinedUpdatePayload(name="Valid Name")
        assert payload.name == "Valid Name"

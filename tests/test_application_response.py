#!/usr/bin/env python3
"""
Test script to verify that ApplicationResponse can handle integer status values.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_application_response():
    """Test that ApplicationResponse can handle Application with integer status."""
    print("🧪 Testing ApplicationResponse with integer status...")
    
    try:
        from app.schemas.application import Application, ApplicationResponse, ApplicationStatusEnum
        
        # Simulate data returned from gRPC service with integer status
        app_data_from_grpc = {
            "id": "test_123",
            "user_id": "user_123",
            "name": "Test App",
            "description": "Test description",
            "workflow_ids": ["workflow_1"],
            "agent_ids": ["agent_1"],
            "status": 1,  # Integer value from gRPC (1 = active)
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z",
            "api_keys": ["key_1"],
            "is_deleted": False
        }
        
        # Create Application model (this should work with the validator)
        app = Application(**app_data_from_grpc)
        print(f"✅ Application created with status: {app.status}")
        assert app.status == ApplicationStatusEnum.ACTIVE
        
        # Create ApplicationResponse (this should also work)
        response = ApplicationResponse(
            success=True,
            message="Application retrieved successfully",
            application=app
        )
        print(f"✅ ApplicationResponse created successfully")
        print(f"   Application status in response: {response.application.status}")
        
        # Test with different status values
        test_cases = [
            (0, ApplicationStatusEnum.UNSPECIFIED),
            (1, ApplicationStatusEnum.ACTIVE),
            (2, ApplicationStatusEnum.INACTIVE),
            (3, ApplicationStatusEnum.SUSPENDED)
        ]
        
        for int_status, expected_enum in test_cases:
            app_data_from_grpc["status"] = int_status
            app = Application(**app_data_from_grpc)
            response = ApplicationResponse(
                success=True,
                message="Test response",
                application=app
            )
            print(f"✅ Status {int_status} -> {response.application.status}")
            assert response.application.status == expected_enum
        
        print("✅ All ApplicationResponse tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ ApplicationResponse test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_paginated_response():
    """Test that PaginatedApplicationResponse can handle multiple applications with integer status."""
    print("\n🧪 Testing PaginatedApplicationResponse...")
    
    try:
        from app.schemas.application import Application, PaginatedApplicationResponse, PaginationMetadata
        
        # Simulate multiple applications from gRPC with integer status
        apps_data = [
            {
                "id": "app_1",
                "user_id": "user_123",
                "name": "App 1",
                "description": "First app",
                "workflow_ids": [],
                "agent_ids": [],
                "status": 1,  # active
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "api_keys": [],
                "is_deleted": False
            },
            {
                "id": "app_2",
                "user_id": "user_123",
                "name": "App 2",
                "description": "Second app",
                "workflow_ids": [],
                "agent_ids": [],
                "status": 2,  # inactive
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "api_keys": [],
                "is_deleted": False
            },
            {
                "id": "app_3",
                "user_id": "user_123",
                "name": "App 3",
                "description": "Third app",
                "workflow_ids": [],
                "agent_ids": [],
                "status": 3,  # suspended
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "api_keys": [],
                "is_deleted": False
            }
        ]
        
        # Create Application models
        applications = [Application(**app_data) for app_data in apps_data]
        print(f"✅ Created {len(applications)} applications")
        
        for i, app in enumerate(applications):
            print(f"   App {i+1} status: {app.status}")
        
        # Create pagination metadata
        metadata = PaginationMetadata(
            total=3,
            total_pages=1,
            current_page=1,
            page_size=50,
            has_next_page=False,
            has_previous_page=False
        )
        
        # Create paginated response
        response = PaginatedApplicationResponse(
            data=applications,
            metadata=metadata
        )
        
        print(f"✅ PaginatedApplicationResponse created successfully")
        print(f"   Total applications: {len(response.data)}")
        print(f"   Metadata total: {response.metadata.total}")
        
        # Verify all status conversions worked
        expected_statuses = ["active", "inactive", "suspended"]
        for i, app in enumerate(response.data):
            assert app.status.value == expected_statuses[i]
            print(f"   App {i+1} status verified: {app.status.value}")
        
        print("✅ All PaginatedApplicationResponse tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ PaginatedApplicationResponse test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 Starting Application Response Tests...\n")
    
    tests = [
        test_application_response,
        test_paginated_response
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All application response tests passed!")
        print("✅ The status conversion fix is working correctly!")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

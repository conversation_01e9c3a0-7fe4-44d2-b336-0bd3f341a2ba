"""
Test module for API Gateway token expiry configuration.
Tests that access tokens expire in 1 day and refresh tokens expire in 7 days.
"""

import pytest
import os
from unittest.mock import patch

# Mock the settings to avoid Redis connection issues during testing
@patch.dict(os.environ, {
    'JWT_SECRET_KEY': 'test-secret-key-at-least-32-chars-long',
    'REDIS_HOST': 'localhost',
    'ORCHESTRATION_SERVER_AUTH_KEY': 'test-key',
    'AGENT_PLATFORM_AUTH_KEY': 'test-key',
    'WORKFLOW_SERVICE_AUTH_KEY': 'test-key',
    'DEPLOYMENT_WORKER_AUTH_KEY': 'test-key',
    'GCS_CRED': 'test-cred',
    'BUCKET_NAME': 'test-bucket'
})
def get_settings():
    from app.core.config import settings
    return settings

settings = get_settings()


class TestAPIGatewayTokenExpiry:
    """Test class for API Gateway token expiry configuration."""

    def test_access_token_expiry_configuration(self):
        """Test that ACCESS_TOKEN_EXPIRE_MINUTES is set to 1440 (1 day)."""
        assert settings.ACCESS_TOKEN_EXPIRE_MINUTES == 1440, (
            f"Expected ACCESS_TOKEN_EXPIRE_MINUTES to be 1440, "
            f"but got {settings.ACCESS_TOKEN_EXPIRE_MINUTES}"
        )

    def test_refresh_token_expiry_configuration(self):
        """Test that REFRESH_TOKEN_EXPIRE_DAYS is set to 7 days."""
        assert settings.REFRESH_TOKEN_EXPIRE_DAYS == 7, (
            f"Expected REFRESH_TOKEN_EXPIRE_DAYS to be 7, "
            f"but got {settings.REFRESH_TOKEN_EXPIRE_DAYS}"
        )

    def test_redis_jwt_access_expire_sec_configuration(self):
        """Test that REDIS_JWT_ACCESS_EXPIRE_SEC is set to 86400 (1 day)."""
        assert settings.REDIS_JWT_ACCESS_EXPIRE_SEC == 86400, (
            f"Expected REDIS_JWT_ACCESS_EXPIRE_SEC to be 86400, "
            f"but got {settings.REDIS_JWT_ACCESS_EXPIRE_SEC}"
        )

    def test_token_expiry_consistency(self):
        """Test that JWT expiry and Redis TTL are consistent."""
        # Access token: 1440 minutes = 86400 seconds
        jwt_expiry_minutes = settings.ACCESS_TOKEN_EXPIRE_MINUTES
        jwt_expiry_seconds = jwt_expiry_minutes * 60
        redis_ttl_seconds = settings.REDIS_JWT_ACCESS_EXPIRE_SEC

        assert jwt_expiry_seconds == redis_ttl_seconds, (
            f"JWT expiry ({jwt_expiry_seconds} seconds) should match "
            f"Redis TTL ({redis_ttl_seconds} seconds)"
        )

        # Refresh token: 7 days = 604800 seconds
        jwt_expiry_days = settings.REFRESH_TOKEN_EXPIRE_DAYS
        jwt_expiry_seconds = jwt_expiry_days * 24 * 60 * 60

        assert jwt_expiry_seconds == 604800, (
            f"Refresh token should expire in 604800 seconds, "
            f"but calculated {jwt_expiry_seconds} seconds"
        )

    def test_cookie_expiry_calculations(self):
        """Test that cookie expiry calculations are correct."""
        # Access token cookie: should be 1 day = 86400 seconds
        access_cookie_expiry = 60 * 60 * 24  # From the fixed code
        expected_access_expiry = 86400

        assert access_cookie_expiry == expected_access_expiry, (
            f"Access token cookie expiry should be {expected_access_expiry} seconds, "
            f"but got {access_cookie_expiry} seconds"
        )

        # Refresh token cookie: should be 7 days = 604800 seconds
        refresh_cookie_expiry = 7 * 24 * 60 * 60  # From the code
        expected_refresh_expiry = 604800

        assert refresh_cookie_expiry == expected_refresh_expiry, (
            f"Refresh token cookie expiry should be {expected_refresh_expiry} seconds, "
            f"but got {refresh_cookie_expiry} seconds"
        )

    def test_configuration_values_summary(self):
        """Test and display all token-related configuration values."""
        print("\n=== API Gateway Token Configuration Summary ===")
        print(f"ACCESS_TOKEN_EXPIRE_MINUTES: {settings.ACCESS_TOKEN_EXPIRE_MINUTES} minutes")
        print(f"ACCESS_TOKEN_EXPIRE_HOURS: {settings.ACCESS_TOKEN_EXPIRE_MINUTES / 60} hours")
        print(f"ACCESS_TOKEN_EXPIRE_DAYS: {settings.ACCESS_TOKEN_EXPIRE_MINUTES / (60 * 24)} days")
        print(f"REFRESH_TOKEN_EXPIRE_DAYS: {settings.REFRESH_TOKEN_EXPIRE_DAYS} days")
        print(f"REDIS_JWT_ACCESS_EXPIRE_SEC: {settings.REDIS_JWT_ACCESS_EXPIRE_SEC} seconds")
        print(f"REDIS_JWT_ACCESS_EXPIRE_HOURS: {settings.REDIS_JWT_ACCESS_EXPIRE_SEC / 3600} hours")
        print("=" * 50)

        # All assertions should pass
        assert settings.ACCESS_TOKEN_EXPIRE_MINUTES == 1440
        assert settings.REFRESH_TOKEN_EXPIRE_DAYS == 7
        assert settings.REDIS_JWT_ACCESS_EXPIRE_SEC == 86400
        assert settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60 == settings.REDIS_JWT_ACCESS_EXPIRE_SEC

import pytest
from unittest.mock import Mock, patch
import grpc
from app.services.application_service import ApplicationServiceClient
from app.grpc_ import analytics_pb2


@pytest.mark.asyncio
class TestApplicationService:
    
    @pytest.fixture
    def mock_application_stub(self):
        with patch('app.services.application_service.analytics_pb2_grpc.ApplicationServiceStub') as mock:
            yield mock

    @pytest.fixture
    def grpc_error(self):
        return grpc.RpcError("Test gRPC error")

    async def test_create_application_success(self, mock_application_stub):
        # Arrange
        client = ApplicationServiceClient()
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Application created successfully"
        
        # Mock application data
        mock_application = Mock()
        mock_application.id = "app_123"
        mock_application.user_id = "user_123"
        mock_application.name = "Test Application"
        mock_application.description = "A test application"
        mock_application.workflow_ids = ["workflow_1", "workflow_2"]
        mock_application.agent_ids = ["agent_1", "agent_2"]
        mock_application.status = analytics_pb2.ApplicationStatus.APPLICATION_STATUS_ACTIVE
        mock_application.created_at = "2024-01-01T00:00:00Z"
        mock_application.updated_at = "2024-01-01T00:00:00Z"
        mock_application.image_ids = []
        
        mock_response.application = mock_application
        mock_application_stub.return_value.CreateApplication.return_value = mock_response

        # Act
        result = await client.create_application(
            user_id="user_123",
            name="Test Application",
            description="A test application",
            workflow_ids=["workflow_1", "workflow_2"],
            agent_ids=["agent_1", "agent_2"]
        )

        # Assert
        assert result["success"] is True
        assert result["message"] == "Application created successfully"
        assert result["application"]["id"] == "app_123"
        assert result["application"]["name"] == "Test Application"
        assert result["application"]["description"] == "A test application"
        assert result["application"]["workflow_ids"] == ["workflow_1", "workflow_2"]
        assert result["application"]["agent_ids"] == ["agent_1", "agent_2"]

    async def test_create_application_error(self, mock_application_stub, grpc_error):
        # Arrange
        client = ApplicationServiceClient()
        mock_application_stub.return_value.CreateApplication.side_effect = grpc_error

        # Act
        result = await client.create_application(
            user_id="user_123",
            name="Test Application",
            description="A test application"
        )

        # Assert
        assert result["success"] is False
        assert "gRPC error" in result["message"]
        assert result["application"] is None

    async def test_get_applications_success(self, mock_application_stub):
        # Arrange
        client = ApplicationServiceClient()
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Applications retrieved successfully"
        mock_response.total_count = 2
        
        # Mock application list
        mock_app1 = Mock()
        mock_app1.id = "app_123"
        mock_app1.user_id = "user_123"
        mock_app1.name = "App 1"
        mock_app1.description = "First app"
        mock_app1.workflow_ids = ["workflow_1"]
        mock_app1.agent_ids = ["agent_1"]
        mock_app1.status = analytics_pb2.ApplicationStatus.APPLICATION_STATUS_ACTIVE
        mock_app1.created_at = "2024-01-01T00:00:00Z"
        mock_app1.updated_at = "2024-01-01T00:00:00Z"
        mock_app1.image_ids = []
        
        mock_app2 = Mock()
        mock_app2.id = "app_456"
        mock_app2.user_id = "user_123"
        mock_app2.name = "App 2"
        mock_app2.description = "Second app"
        mock_app2.workflow_ids = ["workflow_2"]
        mock_app2.agent_ids = ["agent_2"]
        mock_app2.status = analytics_pb2.ApplicationStatus.APPLICATION_STATUS_ACTIVE
        mock_app2.created_at = "2024-01-02T00:00:00Z"
        mock_app2.updated_at = "2024-01-02T00:00:00Z"
        mock_app2.image_ids = []
        
        mock_response.applications = [mock_app1, mock_app2]
        mock_application_stub.return_value.GetApplications.return_value = mock_response

        # Act
        result = await client.get_applications(
            user_id="user_123",
            status="active",
            limit=10,
            offset=0
        )

        # Assert
        assert result["success"] is True
        assert result["message"] == "Applications retrieved successfully"
        assert result["total_count"] == 2
        assert len(result["applications"]) == 2
        assert result["applications"][0]["id"] == "app_123"
        assert result["applications"][1]["id"] == "app_456"

    async def test_get_application_success(self, mock_application_stub):
        # Arrange
        client = ApplicationServiceClient()
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Application retrieved successfully"
        
        # Mock application data
        mock_application = Mock()
        mock_application.id = "app_123"
        mock_application.user_id = "user_123"
        mock_application.name = "Test Application"
        mock_application.description = "A test application"
        mock_application.workflow_ids = ["workflow_1"]
        mock_application.agent_ids = ["agent_1"]
        mock_application.status = analytics_pb2.ApplicationStatus.APPLICATION_STATUS_ACTIVE
        mock_application.created_at = "2024-01-01T00:00:00Z"
        mock_application.updated_at = "2024-01-01T00:00:00Z"
        mock_application.image_ids = []
        
        # Mock metrics data
        mock_metrics = Mock()
        mock_metrics.application_id = "app_123"
        mock_metrics.total_requests = 100
        mock_metrics.successful_requests = 95
        mock_metrics.failed_requests = 5
        mock_metrics.credits_used = 50.0
        mock_metrics.last_request_at = "2024-01-01T12:00:00Z"
        mock_metrics.usage_trend = []
        
        mock_response.application = mock_application
        mock_response.metrics = mock_metrics
        mock_application_stub.return_value.GetApplication.return_value = mock_response

        # Act
        result = await client.get_application(
            application_id="app_123",
            user_id="user_123"
        )

        # Assert
        assert result["success"] is True
        assert result["message"] == "Application retrieved successfully"
        assert result["application"]["id"] == "app_123"
        assert result["application"]["name"] == "Test Application"
        assert result["metrics"]["application_id"] == "app_123"
        assert result["metrics"]["total_requests"] == 100
        assert result["metrics"]["successful_requests"] == 95
        assert result["metrics"]["credits_used"] == 50.0

    async def test_update_application_success(self, mock_application_stub):
        # Arrange
        client = ApplicationServiceClient()
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Application updated successfully"
        
        # Mock updated application data
        mock_application = Mock()
        mock_application.id = "app_123"
        mock_application.user_id = "user_123"
        mock_application.name = "Updated Application"
        mock_application.description = "An updated test application"
        mock_application.workflow_ids = ["workflow_1", "workflow_3"]
        mock_application.agent_ids = ["agent_1", "agent_3"]
        mock_application.status = analytics_pb2.ApplicationStatus.APPLICATION_STATUS_ACTIVE
        mock_application.created_at = "2024-01-01T00:00:00Z"
        mock_application.updated_at = "2024-01-02T00:00:00Z"
        mock_application.image_ids = []
        
        mock_response.application = mock_application
        mock_application_stub.return_value.UpdateApplication.return_value = mock_response

        # Act
        result = await client.update_application(
            application_id="app_123",
            user_id="user_123",
            name="Updated Application",
            description="An updated test application",
            workflow_ids=["workflow_1", "workflow_3"],
            agent_ids=["agent_1", "agent_3"],
            status="active"
        )

        # Assert
        assert result["success"] is True
        assert result["message"] == "Application updated successfully"
        assert result["application"]["id"] == "app_123"
        assert result["application"]["name"] == "Updated Application"
        assert result["application"]["description"] == "An updated test application"

    async def test_delete_application_success(self, mock_application_stub):
        # Arrange
        client = ApplicationServiceClient()
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Application deleted successfully"
        mock_application_stub.return_value.DeleteApplication.return_value = mock_response

        # Act
        result = await client.delete_application(
            application_id="app_123",
            user_id="user_123"
        )

        # Assert
        assert result["success"] is True
        assert result["message"] == "Application deleted successfully"

    async def test_attach_image_to_application_success(self, mock_application_stub):
        # Arrange
        client = ApplicationServiceClient()
        mock_response = Mock()
        mock_response.success = True
        mock_response.message = "Image attached successfully"
        mock_response.image_id = "image_123"
        mock_response.image_url = "https://example.com/images/image_123.jpg"
        mock_application_stub.return_value.AttachImageToApplication.return_value = mock_response

        # Act
        result = await client.attach_image_to_application(
            application_id="app_123",
            user_id="user_123",
            image_name="test_image.jpg",
            image_type="image/jpeg",
            image_data=b"fake_image_data",
            description="Test image"
        )

        # Assert
        assert result["success"] is True
        assert result["message"] == "Image attached successfully"
        assert result["image_id"] == "image_123"
        assert result["image_url"] == "https://example.com/images/image_123.jpg"

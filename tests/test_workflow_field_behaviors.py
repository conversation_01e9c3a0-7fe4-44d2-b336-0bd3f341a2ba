"""
Test cases for workflow field behaviors in API Gateway:
1. is_changes_marketplace field behavior
2. is_customizable field behavior
3. auto_version_on_update field behavior

These tests verify the API Gateway correctly handles the three field behaviors
by testing schema validation and field inclusion.
"""

import pytest
from app.schemas.workflow import WorkflowPatchPayload, WorkflowInDB


class TestWorkflowFieldBehaviors:
    """Test cases for workflow field behaviors in API Gateway"""

    def test_is_changes_marketplace_field_in_schema(self):
        """Test that is_changes_marketplace field is properly included in schemas"""

        # Test 1: WorkflowPatchPayload should accept is_changes_marketplace field
        patch_data = {
            "name": "Updated Workflow",
            "is_changes_marketplace": True
        }

        # This should not raise any validation errors
        patch_payload = WorkflowPatchPayload(**patch_data)
        assert patch_payload.is_changes_marketplace == True
        assert patch_payload.name == "Updated Workflow"

        # Test 2: WorkflowInDB should include is_changes_marketplace field
        workflow_data = {
            "id": "test-workflow-id",
            "name": "Test Workflow",
            "description": "Test Description",
            "workflow_url": "http://example.com/workflow",
            "builder_url": "http://example.com/builder",
            "start_nodes": [],
            "owner_id": "user-123",
            "user_ids": ["user-123"],
            "owner_type": "user",
            "version": "1.0.0",
            "is_changes_marketplace": False
        }

        workflow = WorkflowInDB(**workflow_data)
        assert workflow.is_changes_marketplace == False
        assert workflow.name == "Test Workflow"

    @pytest.mark.asyncio
    async def test_is_customizable_field_behavior(self):
        """Test is_customizable field behavior"""

        # Test 1: When workflow is created from scratch, is_customizable should be True (default)
        with patch.object(self.workflow_client, 'stub') as mock_stub:
            # Mock successful workflow creation response
            mock_response = workflow_pb2.CreateWorkflowResponse()
            mock_response.success = True
            mock_response.message = "Workflow created successfully"
            mock_response.workflow_id = "test-workflow-id"
            mock_stub.createWorkflow.return_value = mock_response

            # Create workflow
            response = await self.workflow_client.create_workflow(
                name="Test Workflow",
                workflow_data={"nodes": [], "edges": []},
                start_nodes=[],
                owner_type="user",
                owner_details={"id": "user-123"},
                description="Test workflow for is_customizable behavior"
            )

            # Verify workflow creation was successful
            assert response.success == True
            assert response.workflow_id == "test-workflow-id"

        # Test 2: Verify is_customizable field can be updated via PATCH
        with patch.object(self.workflow_client, 'stub') as mock_stub:
            mock_response = workflow_pb2.UpdateWorkflowResponse()
            mock_response.success = True
            mock_response.message = "Workflow updated successfully"
            mock_stub.updateWorkflow.return_value = mock_response

            # Update workflow with is_customizable = False
            update_fields = {"is_customizable": False}
            response = await self.workflow_client.patch_workflow(
                workflow_id="test-workflow-id",
                update_fields=update_fields,
                owner_details={"id": "user-123"}
            )

            # Verify update was successful
            assert response.success == True

    @pytest.mark.asyncio
    async def test_auto_version_on_update_field_behavior(self):
        """Test auto_version_on_update field behavior"""

        # Test 1: When workflow is created, auto_version_on_update should be False (default)
        with patch.object(self.workflow_client, 'stub') as mock_stub:
            # Mock successful workflow creation response
            mock_response = workflow_pb2.CreateWorkflowResponse()
            mock_response.success = True
            mock_response.message = "Workflow created successfully with v1.0.0 version"
            mock_response.workflow_id = "test-workflow-id"
            mock_stub.createWorkflow.return_value = mock_response

            # Create workflow
            response = await self.workflow_client.create_workflow(
                name="Test Workflow",
                workflow_data={"nodes": [], "edges": []},
                start_nodes=[],
                owner_type="user",
                owner_details={"id": "user-123"},
                description="Test workflow for auto_version_on_update behavior"
            )

            # Verify workflow creation was successful
            assert response.success == True
            assert response.workflow_id == "test-workflow-id"
            assert "v1.0.0" in response.message  # First version should be created automatically

        # Test 2: When auto_version_on_update is False, updates should not create new versions
        with patch.object(self.workflow_client, 'stub') as mock_stub:
            mock_response = workflow_pb2.UpdateWorkflowResponse()
            mock_response.success = True
            mock_response.message = "Workflow updated successfully"  # No version creation message
            mock_stub.updateWorkflow.return_value = mock_response

            # Update workflow with auto_version_on_update = False (default)
            update_fields = {"name": "Updated Workflow Name"}
            response = await self.workflow_client.patch_workflow(
                workflow_id="test-workflow-id",
                update_fields=update_fields,
                owner_details={"id": "user-123"}
            )

            # Verify update was successful and no new version was created
            assert response.success == True
            assert "version" not in response.message.lower()

        # Test 3: When auto_version_on_update is True, updates should create new versions
        with patch.object(self.workflow_client, 'stub') as mock_stub:
            mock_response = workflow_pb2.UpdateWorkflowResponse()
            mock_response.success = True
            mock_response.message = "Workflow updated successfully and created new version"
            mock_stub.updateWorkflow.return_value = mock_response

            # First enable auto_version_on_update
            update_fields = {"auto_version_on_update": True}
            response = await self.workflow_client.patch_workflow(
                workflow_id="test-workflow-id",
                update_fields=update_fields,
                owner_details={"id": "user-123"}
            )
            assert response.success == True

            # Then update workflow content - should create new version
            update_fields = {"name": "Updated Workflow Name v2"}
            response = await self.workflow_client.patch_workflow(
                workflow_id="test-workflow-id",
                update_fields=update_fields,
                owner_details={"id": "user-123"}
            )

            # Verify update was successful and new version was created
            assert response.success == True
            assert "version" in response.message.lower()


if __name__ == "__main__":
    pytest.main([__file__])

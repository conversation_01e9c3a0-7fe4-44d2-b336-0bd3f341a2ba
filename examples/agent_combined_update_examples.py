#!/usr/bin/env python3
"""
Examples demonstrating how to use the comprehensive Agent Combined Update API.

This script shows various ways to update agent fields using the single
PATCH /agents/{agent_id}/combined endpoint.
"""

import requests
import json
from typing import Dict, Any


class AgentUpdateExamples:
    def __init__(self, base_url: str = "http://localhost:8000", auth_token: str = None):
        self.base_url = base_url
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {auth_token}" if auth_token else None
        }
    
    def update_agent_combined(self, agent_id: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Make a combined update request to the agent API"""
        url = f"{self.base_url}/agents/{agent_id}/combined"
        response = requests.patch(url, json=payload, headers=self.headers)
        return response.json()
    
    def example_1_core_details_only(self, agent_id: str):
        """Example 1: Update only core details"""
        print("=== Example 1: Core Details Only ===")
        
        payload = {
            "name": "Updated Agent Name",
            "description": "This agent has been updated with new core details",
            "system_message": "You are an updated helpful assistant",
            "model_provider": "openai",
            "model_name": "gpt-4",
            "temperature": 0.7,
            "max_tokens": 2000,
            "department": "Engineering",
            "tone": "professional",
            "category": "general"
        }
        
        print(f"Payload: {json.dumps(payload, indent=2)}")
        result = self.update_agent_combined(agent_id, payload)
        print(f"Result: {json.dumps(result, indent=2)}")
        return result
    
    def example_2_knowledge_update(self, agent_id: str):
        """Example 2: Update knowledge base (files and URLs)"""
        print("\n=== Example 2: Knowledge Update ===")
        
        payload = {
            "files": [
                "https://drive.google.com/file/d/abc123/view",
                "https://drive.google.com/file/d/def456/view"
            ],
            "urls": [
                "https://docs.example.com/api-reference",
                "https://wiki.company.com/guidelines"
            ]
        }
        
        print(f"Payload: {json.dumps(payload, indent=2)}")
        result = self.update_agent_combined(agent_id, payload)
        print(f"Result: {json.dumps(result, indent=2)}")
        print("Note: Google Drive sync will be triggered automatically in the background")
        return result
    
    def example_3_capabilities_and_variables(self, agent_id: str):
        """Example 3: Update capabilities and variables"""
        print("\n=== Example 3: Capabilities and Variables ===")
        
        payload = {
            "capabilities": [
                {
                    "title": "Data Analysis",
                    "description": "Can analyze CSV and JSON data files"
                },
                {
                    "title": "Report Generation",
                    "description": "Can generate detailed reports and summaries"
                }
            ],
            "input_modes": ["text", "file"],
            "output_modes": ["text", "json"],
            "response_model": ["structured"],
            "variables": [
                {
                    "name": "api_key",
                    "description": "API key for external service integration",
                    "type": "text",
                    "default_value": "your-api-key-here"
                },
                {
                    "name": "max_results",
                    "description": "Maximum number of results to return",
                    "type": "number",
                    "default_value": 10
                },
                {
                    "name": "config",
                    "description": "Configuration object",
                    "type": "json",
                    "default_value": {"enabled": True, "timeout": 30}
                }
            ]
        }
        
        print(f"Payload: {json.dumps(payload, indent=2)}")
        result = self.update_agent_combined(agent_id, payload)
        print(f"Result: {json.dumps(result, indent=2)}")
        return result
    
    def example_4_comprehensive_update(self, agent_id: str):
        """Example 4: Comprehensive update with multiple sections"""
        print("\n=== Example 4: Comprehensive Update ===")
        
        payload = {
            # Core details
            "name": "Comprehensive Analytics Agent",
            "description": "A fully configured agent for data analysis and reporting",
            "system_message": "You are a specialized data analysis assistant with access to various tools and knowledge sources.",
            "model_provider": "openai",
            "model_name": "gpt-4",
            "temperature": 0.3,
            "max_tokens": 4000,
            "department": "Data Science",
            "tone": "analytical",
            
            # Knowledge
            "files": ["https://drive.google.com/file/d/data-guide/view"],
            "urls": ["https://docs.analytics.com/api"],
            
            # Capabilities
            "capabilities": [
                {"title": "Statistical Analysis", "description": "Perform statistical analysis on datasets"},
                {"title": "Data Visualization", "description": "Create charts and graphs"}
            ],
            "input_modes": ["text", "file", "json"],
            "output_modes": ["text", "json", "chart"],
            
            # MCP Servers and Workflows
            "mcp_server_ids": ["mcp-analytics-server", "mcp-visualization-server"],
            "workflow_ids": ["data-processing-workflow", "report-generation-workflow"],
            
            # Variables
            "variables": [
                {
                    "name": "database_url",
                    "description": "Database connection URL",
                    "type": "text",
                    "default_value": "postgresql://localhost:5432/analytics"
                }
            ],
            
            # Settings
            "tags": ["analytics", "data-science", "reporting"],
            "status": "active",
            "is_customizable": True,
            "example_prompts": [
                "Analyze this dataset and provide insights",
                "Create a summary report of the data",
                "What trends do you see in this data?"
            ]
        }
        
        print(f"Payload: {json.dumps(payload, indent=2)}")
        result = self.update_agent_combined(agent_id, payload)
        print(f"Result: {json.dumps(result, indent=2)}")
        return result
    
    def example_5_settings_only(self, agent_id: str):
        """Example 5: Update only settings and metadata"""
        print("\n=== Example 5: Settings Only ===")
        
        payload = {
            "tags": ["updated", "production", "v2"],
            "status": "active",
            "is_changes_marketplace": False,
            "is_updated": True,
            "is_customizable": True,
            "is_a2a": False,
            "example_prompts": [
                "How can I help you today?",
                "What would you like me to analyze?",
                "Upload a file for analysis"
            ]
        }
        
        print(f"Payload: {json.dumps(payload, indent=2)}")
        result = self.update_agent_combined(agent_id, payload)
        print(f"Result: {json.dumps(result, indent=2)}")
        return result


def main():
    """Run all examples"""
    # Initialize with your API configuration
    examples = AgentUpdateExamples(
        base_url="http://localhost:8000",
        auth_token="your-auth-token-here"  # Replace with actual token
    )
    
    agent_id = "your-agent-id-here"  # Replace with actual agent ID
    
    print("Agent Combined Update API Examples")
    print("=" * 50)
    
    try:
        # Run all examples
        examples.example_1_core_details_only(agent_id)
        examples.example_2_knowledge_update(agent_id)
        examples.example_3_capabilities_and_variables(agent_id)
        examples.example_4_comprehensive_update(agent_id)
        examples.example_5_settings_only(agent_id)
        
        print("\n" + "=" * 50)
        print("All examples completed successfully!")
        print("\nKey Benefits of the Combined Update API:")
        print("- Single request for multiple updates")
        print("- Automatic Google Drive sync for files/URLs")
        print("- Atomic updates (all or nothing)")
        print("- Reduced API calls and better performance")
        
    except Exception as e:
        print(f"Error running examples: {e}")
        print("Make sure to:")
        print("1. Replace 'your-auth-token-here' with a valid auth token")
        print("2. Replace 'your-agent-id-here' with a valid agent ID")
        print("3. Ensure the API server is running")


if __name__ == "__main__":
    main()

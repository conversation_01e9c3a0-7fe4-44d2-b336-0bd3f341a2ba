from fastapi import APIRouter, Depends, HTTPException

from app.core.auth_guard import role_required
from app.schemas.livekit import LiveKitRoomCreateRequest, LiveKitRoomCreateResponse
from app.services.livekit_service import LiveKitService

livekit_router = APIRouter(prefix="/livekit")


@livekit_router.post(
    "/room/create",
    response_model=LiveKitRoomCreateResponse,
    summary="Create a new LiveKit room",
    description="Create a new LiveKit room with the given agent ID, chat type, and conversation ID",
    tags=["LiveKit"],
    responses={
        200: {
            "description": "Room created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "token": "token",
                    }
                }
            },
        },
        500: {
            "description": "Internal server error",
        },
    },
)
async def create_livekit_room(
    request: LiveKitRoomCreateRequest, current_user=Depends(role_required(["user"]))
):
    """
    Create a new LiveKit room

    - For single chat type: agent_id refers to a single agent
    - For multi chat type: agent_id refers to an agent group ID
    """

    try:
        return await LiveKitService.create_room(
            agent_id=request.agent_id,
            chat_type=request.chat_type,
            current_user=current_user,
            conversation_id=request.conversation_id,
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

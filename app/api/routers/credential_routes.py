from fastapi import APIRout<PERSON>, Depends, HTTPException, status
from app.services.user_service import UserServiceClient
from app.core.auth_guard import role_required
from app.schemas.user import (
    CredentialCreate,
    CredentialResponse,
    CredentialListResponse,
    CredentialDetailsResponse,
    CredentialDeleteResponse,
    CredentialInfo,
    CredentialUpdate,
    CredentialUpdateResponse,
)
from app.utils.parse_error import parse_error
from google.protobuf.json_format import MessageToDict
import logging

credential_router = APIRouter(prefix="/credentials", tags=["credentials"])
user_service = UserServiceClient()


@credential_router.post("", response_model=CredentialResponse)
async def create_credential(
    credential_data: CredentialCreate, current_user: dict = Depends(role_required(["user"]))
):
    try:
        response = await user_service.create_credential(
            owner_id=current_user["user_id"],
            key_name=credential_data.key_name,
            value=credential_data.value,
            description=credential_data.description,
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.message)

        return CredentialResponse(
            success=response.success,
            message=response.message,
            id=response.id,
            key_name=response.key_name,
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"Error in create_credential: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@credential_router.get("", response_model=CredentialListResponse)
async def list_credentials(current_user: dict = Depends(role_required(["user"]))):
    try:
        logging.info(f"Listing credentials for user: {current_user['user_id']}")
        response = await user_service.list_credentials(owner_id=current_user["user_id"])
        if not response.success:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=response.message)

        logging.info(f"Credentials response: {response}")

        # Convert gRPC response to Pydantic models
        credential_list = []
        for cred in response.credentials:
            # Convert protobuf message to dict
            cred_dict = MessageToDict(cred, preserving_proto_field_name=True)

            # Create Pydantic model from dict
            credential_list.append(
                CredentialInfo(
                    id=cred_dict.get("id"),
                    key_name=cred_dict.get("key_name"),
                    description=cred_dict.get("description"),
                    value=cred_dict.get("value"),
                    created_at=cred_dict.get("created_at"),
                    updated_at=cred_dict.get("updated_at"),
                    last_used_at=cred_dict.get("last_used_at"),
                )
            )

        return CredentialListResponse(
            success=response.success, message=response.message, credentials=credential_list
        )
    except Exception as e:
        logging.error(f"Error in list_credentials: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@credential_router.get("/{credential_id}", response_model=CredentialDetailsResponse)
async def get_credential(credential_id: str, current_user: dict = Depends(role_required(["user"]))):
    try:
        logging.info(f"Getting credential with ID: {credential_id}")
        response = await user_service.get_credential(
            credential_id=credential_id, owner_id=current_user["user_id"]
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=response.message)

        logging.info(f"Credential response: {response}")

        # Convert gRPC response to Pydantic model
        credential = None
        if response.credential:
            cred_dict = MessageToDict(response.credential, preserving_proto_field_name=True)

            credential = CredentialInfo(
                id=cred_dict.get("id"),
                key_name=cred_dict.get("key_name"),
                description=cred_dict.get("description"),
                value=cred_dict.get("value"),
                created_at=cred_dict.get("created_at"),
                updated_at=cred_dict.get("updated_at"),
                last_used_at=cred_dict.get("last_used_at"),
            )

        return CredentialDetailsResponse(
            success=response.success, message=response.message, credential=credential
        )
    except Exception as e:
        logging.error(f"Error in get_credential: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@credential_router.delete("/{credential_id}", response_model=CredentialDeleteResponse)
async def delete_credential(
    credential_id: str, current_user: dict = Depends(role_required(["user"]))
):
    try:
        response = await user_service.delete_credential(
            credential_id=credential_id, owner_id=current_user["user_id"]
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=response.message)

        return CredentialDeleteResponse(success=response.success, message=response.message)
    except Exception as e:
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@credential_router.put("/{credential_id}", response_model=CredentialResponse)
async def update_credential(
    credential_id: str,
    credential_data: CredentialUpdate,
    current_user: dict = Depends(role_required(["user"])),
):
    try:
        response = await user_service.update_credential(
            credential_id=credential_id,
            owner_id=current_user["user_id"],
            key_name=credential_data.key_name,
            value=credential_data.value,
            description=credential_data.description,
        )
        if not response.success:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail=response.message)

        logging.info(f"Updated credential response: {response}")
        return CredentialResponse(
            success=response.success,
            message=response.message,
            id=response.id,
            key_name=response.key_name,
        )
    except Exception as e:
        logging.error(f"Error in update_credential: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])

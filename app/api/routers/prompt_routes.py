import logging

from fastapi import APIRouter, Depends, HTTPException, status

from app.core.auth_guard import role_required
from app.core.config import settings
from app.schemas.prompt import (
    PromptImprovementRequest,
    PromptImprovementResponse,
    UserPromptImprovementRequest,
    UserPromptImprovementResponse,
)
from app.services.openai_service import OpenAIService

logger = logging.getLogger(__name__)

prompt_router = APIRouter(prefix="/prompts", tags=["prompts"])

# Initialize OpenAI service (using Requesty router)
openai_service = OpenAIService()


@prompt_router.post(
    "/improve",
    response_model=PromptImprovementResponse,
    summary="Improve an agent system prompt",
    description="Uses OpenAI through Requesty to improve an agent's system prompt for better performance",
)
async def improve_system_prompt(
    request: PromptImprovementRequest,
    current_user: dict = Depends(role_required(["user", "admin"])),
) -> PromptImprovementResponse:
    """
    Improve an agent's system prompt using OpenAI through Requesty.

    Args:
        request: The prompt improvement request containing the original prompt and optional context

    Returns:
        PromptImprovementResponse: The original and improved prompts
    """
    try:
        logger.info("Improving system prompt with Requesty")

        # Check if Requesty API key is set
        if not settings.REQUESTY_API_KEY:
            logger.error("Requesty API key is not set")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Requesty service is not configured. Please set REQUESTY_API_KEY in the environment variables.",
            )

        # Call Requesty service to improve the prompt
        improved_prompt = openai_service.improve_system_prompt(
            request.original_prompt, request.agent_context
        )

        return PromptImprovementResponse(
            original_prompt=request.original_prompt, improved_prompt=improved_prompt
        )

    except Exception as e:
        logger.error(f"Error improving system prompt: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error improving system prompt: {str(e)}",
        )


@prompt_router.post(
    "/improve-user-prompt",
    response_model=UserPromptImprovementResponse,
    summary="Improve a user prompt",
    description="Uses OpenAI through Requesty to enhance user prompts to be more concise, clear, and comprehensive",
)
async def improve_user_prompt(
    request: UserPromptImprovementRequest,
    current_user: dict = Depends(role_required(["user", "admin"])),
) -> UserPromptImprovementResponse:
    """
    Improve a user's prompt to be more concise, clear, and comprehensive.

    Args:
        request: The user prompt improvement request containing the original prompt

    Returns:
        UserPromptImprovementResponse: The original and improved user prompts
    """
    try:
        logger.info("Improving user prompt with Requesty")

        # Check if Requesty API key is set
        if not settings.REQUESTY_API_KEY:
            logger.error("Requesty API key is not set")
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Requesty service is not configured. Please set REQUESTY_API_KEY in the environment variables.",
            )

        # Call Requesty service to improve the user prompt
        improved_prompt = openai_service.improve_user_prompt(request.original_prompt, request.mode)

        return UserPromptImprovementResponse(
            original_prompt=request.original_prompt,
            improved_prompt=improved_prompt,
            mode=request.mode,
        )

    except Exception as e:
        logger.error(f"Error improving user prompt: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error improving user prompt: {str(e)}",
        )

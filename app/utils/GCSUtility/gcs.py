import os
import json
import time
import tempfile
from google.cloud import storage
from datetime import timedelta
from base64 import b64decode

class GCSUtility:
    def __init__(self):
        self.client = self._create_client()
        self.bucket = self.client.bucket(os.getenv("BUCKET_NAME"))

    def _create_client(self):
        base64_credentials = os.getenv("GCS_CRED")
        if not base64_credentials:
            raise Exception("GCS_BASE64_CREDENTIALS not found in environment.")

        # Decode base64 -> JSON -> temp credentials file
        decoded = b64decode(base64_credentials).decode("utf-8")
        creds_dict = json.loads(decoded)

        with tempfile.NamedTemporaryFile(mode='w+', delete=False, suffix='.json') as temp_cred:
            json.dump(creds_dict, temp_cred)
            temp_cred.flush()
            return storage.Client.from_service_account_json(temp_cred.name)

    def generate_presigned_url(self, file_name: str, file_type: str, file_path: str):
        file_name = file_name.replace(" ", "").replace("..", "")
        file_name = ''.join(c for c in file_name if c.isalnum() or c in ('.', '_', '-'))

        object_key = f"{file_path}/{int(time.time())}-{file_name}"
        blob = self.bucket.blob(object_key)

        try:
            url = blob.generate_signed_url(
                version="v4",
                expiration=timedelta(minutes=60),
                method="PUT",
                content_type=file_type,
            )

            return {
                "success": True,
                "url": url
            }

        except Exception as e:
            return {
                "success": False,
                "message": str(e)
            }

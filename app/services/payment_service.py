import grpc
from typing import Optional
from datetime import datetime
from google.protobuf.timestamp_pb2 import Timestamp
import logging
from fastapi import HTTPException

from app.core.config import settings
from app.grpc_ import payment_pb2, payment_pb2_grpc

logger = logging.getLogger(__name__)

class PaymentServiceClient:
    def __init__(self):
        host = getattr(settings, "PAYMENT_SERVICE_HOST", "localhost")
        port = getattr(settings, "PAYMENT_SERVICE_PORT", "50061")
        payment_address = f"{host}:{port}"
        logger.info(f"Initializing PaymentServiceClient for address: {payment_address}")
        self.channel = grpc.aio.insecure_channel(payment_address)
        self.stub = payment_pb2_grpc.PaymentServiceStub(self.channel)

    async def close(self):
        if self.channel:
            await self.channel.close()
            logger.info("PaymentServiceClient channel closed.")

    def _handle_error(self, e: grpc.aio.AioRpcError):
        status_code = e.code()
        details = e.details()
        if status_code == grpc.StatusCode.NOT_FOUND:
            raise HTTPException(status_code=404, detail=details)
        elif status_code == grpc.StatusCode.INVALID_ARGUMENT:
            raise HTTPException(status_code=400, detail=details)
        else:
            raise HTTPException(status_code=500, detail=details)

    async def create_checkout_session(self, org_id: str, plan_id_code: str, success_url: str, cancel_url: str):
        request = payment_pb2.CreateCheckoutSessionRequest(
            organisation_id=org_id,
            plan_id_code=plan_id_code,
            success_url=success_url,
            cancel_url=cancel_url,
        )
        try:
            return await self.stub.CreateCheckoutSession(request)
        except grpc.aio.AioRpcError as e:
            self._handle_error(e)

    async def create_customer_portal_session(self, org_id: str, return_url: str):
        request = payment_pb2.CreateCustomerPortalSessionRequest(
            organisation_id=org_id,
            return_url=return_url,
        )
        try:
            return await self.stub.CreateCustomerPortalSession(request)
        except grpc.aio.AioRpcError as e:
            self._handle_error(e)

    async def get_subscription(self, org_id: str):
        request = payment_pb2.GetSubscriptionRequest(organisation_id=org_id)
        try:
            logger.info("RPC: GetSubscription", extra={"org_id": org_id})
            return await self.stub.GetSubscription(request)
        except grpc.aio.AioRpcError as e:
            self._handle_error(e)

    async def cancel_subscription(self, org_id: str):
        request = payment_pb2.CancelSubscriptionRequest(organisation_id=org_id)
        try:
            return await self.stub.CancelSubscription(request)
        except grpc.aio.AioRpcError as e:
            self._handle_error(e)

    async def deduct_credits(self, org_id: str, user_id: str, agent_id: str, input_tokens: int, output_tokens: int, consumed_credits: float, description: str):
        request = payment_pb2.DeductCreditsRequest(
            organisation_id=org_id,
            user_id=user_id,
            agent_id=agent_id,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            consumed_credits=consumed_credits,
            description=description,
        )
        try:
            return await self.stub.DeductCredits(request)
        except grpc.aio.AioRpcError as e:
            self._handle_error(e)

    async def get_credit_balance(self, org_id: str):
        request = payment_pb2.GetCreditBalanceRequest(organisation_id=org_id)
        try:
            return await self.stub.GetCreditBalance(request)
        except grpc.aio.AioRpcError as e:
            self._handle_error(e)

    async def get_token_usage(self, org_id: str, user_id: Optional[str], start_date: Optional[datetime], end_date: Optional[datetime]):
        start_date_ts = Timestamp()
        if start_date:
            start_date_ts.FromDatetime(start_date)
        end_date_ts = Timestamp()
        if end_date:
            end_date_ts.FromDatetime(end_date)
        
        logger.info("RPC: GetTokenUsage")

        request = payment_pb2.GetTokenUsageRequest(
            organisation_id=org_id,
            user_id=user_id,
            start_date=start_date_ts,
            end_date=end_date_ts,
        )
        try:
            return await self.stub.GetTokenUsage(request)
        except grpc.aio.AioRpcError as e:
            logger.error("RPC: GetTokenUsage - Error", exc_info=True)
            self._handle_error(e)

    async def handle_stripe_webhook(self, payload: str, signature: str):
        logger.info("Received Stripe webhook", payload=payload, signature=signature)
        request = payment_pb2.StripeWebhookRequest(payload=payload, signature=signature)
        try:
            return await self.stub.HandleStripeWebhook(request)
        except grpc.aio.AioRpcError as e:
            self._handle_error(e)

    async def create_plan(self, plan_data: dict):
        request = payment_pb2.CreatePlanRequest(**plan_data)
        try:
            return await self.stub.CreatePlan(request)
        except grpc.aio.AioRpcError as e:
            self._handle_error(e)

    async def list_plans(self, page: int = 1, page_size: int = 10):
        request = payment_pb2.ListPlansRequest(page=page, page_size=page_size)
        try:
            return await self.stub.ListPlans(request)
        except grpc.aio.AioRpcError as e:
            self._handle_error(e)

    async def activate_default_plan(self, org_id: str):
        logger.info("RPC: ActivateDefaultPlan", extra={"org_id": org_id})
        request = payment_pb2.ActivateDefaultPlanRequest(organisation_id=org_id)
        try:
            return await self.stub.ActivateDefaultPlan(request)
        except grpc.aio.AioRpcError as e:
            self._handle_error(e)

    async def calculate_credits(self, input_tokens: int, output_tokens: int, input_price_per_token: float, output_price_per_token: float):
        """Calculate credits based on pricing information."""
        request = payment_pb2.CalculateCreditsRequest(
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            input_price_per_token=input_price_per_token,
            output_price_per_token=output_price_per_token,
        )
        try:
            return await self.stub.CalculateCredits(request)
        except grpc.aio.AioRpcError as e:
            self._handle_error(e)

    # --- Topup Plan Methods ---

    async def create_topup_checkout_session(self, org_id: str, topup_plan_id_code: str, success_url: str, cancel_url: str):
        """Create a checkout session for topup purchase."""
        request = payment_pb2.CreateTopupCheckoutSessionRequest(
            organisation_id=org_id,
            topup_plan_id_code=topup_plan_id_code,
            success_url=success_url,
            cancel_url=cancel_url,
        )
        try:
            logger.info("RPC: CreateTopupCheckoutSession", extra={"org_id": org_id, "topup_plan": topup_plan_id_code})
            return await self.stub.CreateTopupCheckoutSession(request)
        except grpc.aio.AioRpcError as e:
            self._handle_error(e)

    async def list_topup_plans(self, page: int = 1, page_size: int = 10):
        """List all available topup plans."""
        request = payment_pb2.ListTopupPlansRequest(page=page, page_size=page_size)
        try:
            logger.info("RPC: ListTopupPlans")
            return await self.stub.ListTopupPlans(request)
        except grpc.aio.AioRpcError as e:
            self._handle_error(e)

    async def create_topup_plan(self, plan_data: dict):
        """Create a new topup plan."""
        request = payment_pb2.CreateTopupPlanRequest(**plan_data)
        try:
            logger.info("RPC: CreateTopupPlan", extra={"plan_name": plan_data.get("name")})
            return await self.stub.CreateTopupPlan(request)
        except grpc.aio.AioRpcError as e:
            self._handle_error(e)

    async def get_payment_transactions(self, org_id: str, page: int = 1, page_size: int = 10):
        """Get all payment transactions for an organization."""
        request = payment_pb2.GetPaymentTransactionsRequest(
            organisation_id=org_id,
            page=page,
            page_size=page_size
        )
        try:
            logger.info("RPC: GetPaymentTransactions", extra={"org_id": org_id})
            return await self.stub.GetPaymentTransactions(request)
        except grpc.aio.AioRpcError as e:
            self._handle_error(e)
from fastapi import Request, HTTPException, Security, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ead<PERSON>, HTTPBearer, HTTPAuthorizationCredentials
from typing import List, Dict, Any, Optional, Callable
from jose import jwt
from app.core.config import settings
from app.core.auth_guard import BaseAuthGuard

# Reuse your existing classes
auth_guard = BaseAuthGuard()

bearer_scheme = HTTPBearer(auto_error=False)

SERVER_AUTH_HEADERS = {
    "x-server-auth-key": APIKeyHeader(name="X-Server-Auth-Key", auto_error=False),
    "x-orchestration-key": <PERSON><PERSON><PERSON>Header(name="X-Orchestration-Key", auto_error=False),
    "x-workflow-service-key": <PERSON><PERSON><PERSON>Header(name="X-Workflow-Service-Key", auto_error=False),
    "x-agent-platform-key": APIKeyHeader(name="X-Agent-Platform-Key", auto_error=False),
}


def general_auth_required(allowed_roles: Optional[List[str]] = None):
    async def _auth_dependency(
        request: Request,
        bearer: Optional[HTTPAuthorizationCredentials] = Security(bearer_scheme),
        server_keys: List[Optional[str]] = Security(
            lambda: [hdr() for hdr in SERVER_AUTH_HEADERS.values()]
        ),
    ) -> Dict[str, Any]:
        # 1. Try Bearer Auth (User)
        if bearer:
            try:
                current_user = await auth_guard(bearer)
                if allowed_roles:
                    role = current_user.get("role")
                    if role not in allowed_roles:
                        raise HTTPException(status_code=403, detail="Insufficient permissions")
                return {"type": "user", "data": current_user}
            except Exception as e:
                raise HTTPException(status_code=401, detail=f"User authentication failed: {str(e)}")

        # 2. Try Server Keys
        for idx, key in enumerate(server_keys):
            expected = list(SERVER_AUTH_HEADERS.values())[idx]
            if key == expected:
                header_name = list(SERVER_AUTH_HEADERS.keys())[idx]
                return {"type": "server", "data": {"source": header_name}}

        # 3. Neither worked
        raise HTTPException(status_code=401, detail="Missing or invalid authentication")

    return _auth_dependency

